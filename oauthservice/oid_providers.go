package oauthservice

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gorilla/mux"
	log "github.com/sirupsen/logrus"
	"gopkg.in/mgo.v2"
)

// OIDCDiscoveryResponse represents the well-known OpenID Configuration
type OIDCDiscoveryResponse struct {
	Issuer                string   `json:"issuer"`
	AuthorizationEndpoint string   `json:"authorization_endpoint"`
	TokenEndpoint         string   `json:"token_endpoint"`
	UserinfoEndpoint      string   `json:"userinfo_endpoint"`
	JwksURI               string   `json:"jwks_uri"`
	ScopesSupported       []string `json:"scopes_supported"`
	ClaimsSupported       []string `json:"claims_supported"`
}

// DiscoverOIDCConfiguration fetches the OpenID Connect configuration from the well-known endpoint
func DiscoverOIDCConfiguration(issuerURL string) (*OIDCDiscoveryResponse, error) {
	// Ensure the issuer URL doesn't end with a slash
	if issuerURL[len(issuerURL)-1] == '/' {
		issuerURL = issuerURL[:len(issuerURL)-1]
	}

	// Construct the well-known URL
	wellKnownURL := fmt.Sprintf("%s/.well-known/openid-configuration", issuerURL)

	// Fetch the configuration
	resp, err := http.Get(wellKnownURL)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch OIDC configuration: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to fetch OIDC configuration: status code %d", resp.StatusCode)
	}

	// Parse the response
	var config OIDCDiscoveryResponse
	if err := json.NewDecoder(resp.Body).Decode(&config); err != nil {
		return nil, fmt.Errorf("failed to parse OIDC configuration: %v", err)
	}

	return &config, nil
}

// Helper functions for validation
func validateOIDCConfig(config *OIDCProviderConfig) error {
	if config.Name == "" {
		return fmt.Errorf("name is required")
	}
	if config.ClientID == "" {
		return fmt.Errorf("client ID is required")
	}
	if config.ClientSecret == "" {
		return fmt.Errorf("client secret is required")
	}
	if config.Issuer == "" {
		return fmt.Errorf("issuer is required")
	}
	if _, err := url.Parse(config.Issuer); err != nil {
		return fmt.Errorf("invalid issuer URL format: %s", config.Issuer)
	}
	// Mandatory scopes that must be included
	mandatoryScopes := "openid profile email"

	// If no scopes are provided, just use the mandatory ones
	if config.Scope == "" {
		config.Scope = mandatoryScopes
		return nil
	}

	// Create a new string with mandatory_scopes + received_scopes
	combinedScopes := mandatoryScopes + " " + config.Scope

	// Split the string by space
	scopesList := strings.Fields(combinedScopes)

	// Turn the list into a set to lose duplicates
	scopesSet := make(map[string]struct{})
	for _, scope := range scopesList {
		scopesSet[scope] = struct{}{}
	}

	// Turn the set back to string and use it to update config
	uniqueScopes := make([]string, 0, len(scopesSet))
	for scope := range scopesSet {
		uniqueScopes = append(uniqueScopes, scope)
	}

	config.Scope = strings.Join(uniqueScopes, " ")
	return nil
}

// checkSafe to RemoveActiveProvider checks if it's safe to remove (delete/deactivate) an active provider
func (service *Service) checkSafeToRemoveActiveProvider(mgr *Manager, providerID string) error {
	// Check if this is the last active provider
	activeProviders, err := mgr.ListOIDCProviders(true)
	if err != nil {
		log.Errorf("Failed to check active OIDC providers: %v", err)
		return fmt.Errorf("failed to check active OIDC providers: %v", err)
	}

	// Find current provider in active list to determine if it's currently active
	isCurrentProviderActive := false
	activeProvidersCount := len(activeProviders)
	for _, p := range activeProviders {
		if p.ID == providerID {
			isCurrentProviderActive = true
			break
		}
	}

	// Only need to check further if this provider is currently active
	if isCurrentProviderActive {
		// Get password login status
		passwordStatus, err := mgr.GetPasswordLoginStatus()
		if err != nil {
			log.Errorf("Failed to check password login status: %v", err)
			return fmt.Errorf("failed to check password login status: %v", err)
		}

		// If this is the last active provider and password login is disabled
		if activeProvidersCount <= 1 && !passwordStatus.Enabled {
			log.Error("Cannot remove the last OIDC provider when password login is disabled")
			return fmt.Errorf("cannot remove the last OIDC provider when password login is disabled")
		}
	}

	return nil
}

// OIDCProviderPublicResponse represents OIDC provider public data without sensitive fields
type OIDCProviderPublicResponse struct {
	ID         string `json:"id"`
	Name       string `json:"name"`
	Issuer     string `json:"issuer"`
	Scope      string `json:"scope"`
	ClaimKey   string `json:"claimKey,omitempty"`
	ClaimValue string `json:"claimValue,omitempty"`
	Active     bool   `json:"active"`
	CreatedAt  int64  `json:"createdAt"`
	UpdatedAt  int64  `json:"updatedAt"`
}

// toPublicResponse converts an OIDCProviderConfig to OIDCProviderPublicResponse
func (c *OIDCProviderConfig) toPublicResponse() OIDCProviderPublicResponse {
	return OIDCProviderPublicResponse{
		ID:         c.ID,
		Name:       c.Name,
		Issuer:     c.Issuer,
		Scope:      c.Scope,
		ClaimKey:   c.ClaimKey,
		ClaimValue: c.ClaimValue,
		Active:     c.Active,
		CreatedAt:  c.CreatedAt,
		UpdatedAt:  c.UpdatedAt,
	}
}

// GetOIDCProviderLogsHandler returns the OIDC provider logs
func (service *Service) GetOIDCProviderLogsHandler(w http.ResponseWriter, r *http.Request) {

	mgr := NewManager(r)

	logs, err := mgr.GetOIDCProviderLogs()
	if err != nil {
		log.Error("Error getting OIDC provider logs:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(logs)
}

// GetOIDCProviderHandler retrieves an OIDC provider by ID
func (service *Service) GetOIDCProviderHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]
	if id == "" {
		http.Error(w, "Missing provider ID", http.StatusBadRequest)
		return
	}

	mgr := NewManager(r)
	provider, err := mgr.GetOIDCProvider(id)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}
	if provider == nil {
		http.Error(w, "Provider not found", http.StatusNotFound)
		return
	}

	// Convert to response without sensitive data
	json.NewEncoder(w).Encode(provider.toPublicResponse())
}

// ListOIDCProvidersHandler returns all OIDC providers without sensitive fields
func (service *Service) ListOIDCProvidersHandler(w http.ResponseWriter, r *http.Request) {
	onlyActive := r.URL.Query().Get("active") == "true"

	mgr := NewManager(r)
	providers, err := mgr.ListOIDCProviders(onlyActive)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Convert to response objects without sensitive data
	responses := make([]OIDCProviderPublicResponse, len(providers))
	for i, provider := range providers {
		responses[i] = provider.toPublicResponse()
	}

	// Ensure we always return a results object with an array (even if empty)
	response := map[string]interface{}{
		"results": responses,
	}

	json.NewEncoder(w).Encode(response)
}

// ListPublicOIDCProvidersHandler returns only active OIDC providers with limited information
func (service *Service) ListPublicOIDCProvidersHandler(w http.ResponseWriter, r *http.Request) {
	mgr := NewManager(r)
	providers, err := mgr.ListOIDCProviders(true)
	if err != nil {
		http.Error(w, err.Error(), http.StatusInternalServerError)
		return
	}

	// Convert to response objects with only public fields
	responses := make([]OIDCProviderPublicResponse, len(providers))
	for i, provider := range providers {
		responses[i] = provider.toPublicResponse()
	}

	json.NewEncoder(w).Encode(responses)
}

// GetPasswordLoginStatusHandler handles the API request to get the current password login status
func (service *Service) GetPasswordLoginStatusHandler(w http.ResponseWriter, r *http.Request) {
	mgr := NewManager(r)
	status, err := mgr.GetPasswordLoginStatus()
	if err != nil {
		log.Error("Error getting password login status:", err)
		http.Error(w, http.StatusText(http.StatusInternalServerError), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"enabled":   status.Enabled,
		"updatedAt": status.UpdatedAt,
	})
}

// handlePasswordLoginAction handles password login enable/disable actions
func (service *Service) handlePasswordLoginAction(mgr *Manager, action string) error {
	if action == "" {
		return nil
	}

	log.Infof("Processing password login action: %s", action)
	switch action {
	case "enable":
		log.Info("Enabling password login")
		return mgr.EnablePasswordLogin()
	case "disable":
		log.Info("Checking active OIDC providers before disabling password login")
		activeProviders, err := mgr.ListOIDCProviders(true)
		if err != nil {
			log.Errorf("Failed to check active OIDC providers: %v", err)
			return fmt.Errorf("failed to check active OIDC providers: %v", err)
		}
		if len(activeProviders) == 0 {
			log.Error("Cannot disable password login: no active OIDC providers")
			return fmt.Errorf("cannot disable password login: no active OIDC providers available")
		}
		log.Info("Found active OIDC providers, proceeding with password login disable")
		return mgr.DisablePasswordLogin()
	default:
		log.Errorf("Invalid password login action: %s", action)
		return fmt.Errorf("invalid password login action: %s", action)
	}
}

// handleOIDCProviderAction handles OIDC provider management actions
func (service *Service) handleOIDCProviderAction(mgr *Manager, action string, config *OIDCProviderConfig, providerID string, newLogs *OIDCProviderLogs) error {
	if action == "" {
		log.Info("No OIDC provider action specified, skipping")
		return nil
	}

	if action != "create" && providerID == "" {
		log.Error("Provider ID is required for non-create actions")
		return fmt.Errorf("provider ID is required for %s action", action)
	}

	log.Infof("Processing OIDC provider action: %s", action)
	switch action {
	case "create":
		log.Info("Creating new OIDC provider")
		if config == nil {
			log.Error("Config is required for create action")
			return fmt.Errorf("config is required for create action")
		}
		// Validate the config
		log.Info("Validating OIDC provider config")
		if err := validateOIDCConfig(config); err != nil {
			log.Errorf("Config validation failed: %v", err)
			return err
		}
		// Discover OIDC configuration
		log.Infof("Discovering OIDC configuration from issuer: %s", config.Issuer)
		if _, err := DiscoverOIDCConfiguration(config.Issuer); err != nil {
			log.Errorf("OIDC configuration discovery failed: %v", err)
			return err
		}
		// Set timestamps
		now := time.Now().Unix()
		config.CreatedAt = now
		config.UpdatedAt = now
		config.Active = true

		// Check for duplicate names
		log.Info("Checking for duplicate provider names")
		existing, err := mgr.ListOIDCProviders(false)
		if err != nil {
			log.Errorf("Failed to list existing providers: %v", err)
			return err
		}
		for _, p := range existing {
			if p.Name == config.Name {
				log.Errorf("Provider with name %s already exists", config.Name)
				return fmt.Errorf("provider with name %s already exists", config.Name)
			}
		}
		log.Info("Creating OIDC provider in database")
		if err := mgr.CreateOIDCProvider(config); err != nil {
			return err
		}
		// Update logs with the new provider ID
		newLogs.OIDCProviderID = config.ID
		return nil

	case "update":
		log.Infof("Updating OIDC provider with ID: %s", providerID)
		if config == nil {
			log.Error("Config is required for update action")
			return fmt.Errorf("config is required for update action")
		}
		existing, err := mgr.GetOIDCProvider(providerID)
		if err != nil {
			log.Errorf("Failed to get existing provider: %v", err)
			return fmt.Errorf("failed to get existing provider: %v", err)
		}
		if existing == nil {
			log.Errorf("Provider not found: %s", providerID)
			return fmt.Errorf("provider not found: %s", providerID)
		}

		log.Info("Updating provider fields")
		// Update only provided fields
		if config.Name != "" {
			existing.Name = config.Name
		}
		if config.Issuer != "" {
			existing.Issuer = config.Issuer
		}
		if config.ClientID != "" {
			existing.ClientID = config.ClientID
		}
		if config.ClientSecret != "" {
			existing.ClientSecret = config.ClientSecret
		}
		if config.Scope != "" {
			existing.Scope = config.Scope
		}
		if config.ClaimKey != "" {
			existing.ClaimKey = config.ClaimKey
		}
		if config.ClaimValue != "" {
			existing.ClaimValue = config.ClaimValue
		}

		log.Info("Validating updated config")
		if err := validateOIDCConfig(existing); err != nil {
			log.Errorf("Config validation failed: %v", err)
			return err
		}

		if config.Issuer != "" {
			log.Info("Discovering OIDC configuration for updated issuer")
			if _, err := DiscoverOIDCConfiguration(existing.Issuer); err != nil {
				log.Errorf("OIDC configuration discovery failed: %v", err)
				return err
			}
		}

		existing.UpdatedAt = time.Now().Unix()
		log.Info("Saving updated provider to database")
		return mgr.UpdateOIDCProvider(existing)

	case "delete":
		log.Infof("Deleting OIDC provider with ID: %s", providerID)
		if err := service.checkSafeToRemoveActiveProvider(mgr, providerID); err != nil {
			return err
		}
		return mgr.DeleteOIDCProvider(providerID)

	case "activate":
		log.Infof("Activating OIDC provider with ID: %s", providerID)
		return mgr.ActivateOIDCProvider(providerID)

	case "deactivate":
		log.Infof("Deactivating OIDC provider with ID: %s", providerID)
		if err := service.checkSafeToRemoveActiveProvider(mgr, providerID); err != nil {
			return err
		}
		return mgr.DeactivateOIDCProvider(providerID)

	default:
		log.Errorf("Invalid action: %s", action)
		return fmt.Errorf("invalid action: %s", action)
	}
}

// HandleCLICommand processes OIDC provider and password login management from CLI arguments
func (service *Service) HandleCLICommand(session *mgo.Session, action string, config *OIDCProviderConfig, providerID string, passwordAction string, version int) error {
	log.Info("HandleCLICommand - Starting with parameters:")
	log.Infof("Action: %s, ProviderID: %s, PasswordAction: %s, Version: %d", action, providerID, passwordAction, version)
	if config != nil {
		log.Infof("Config details - Name: %s, Issuer: %s, ClientID: %s, Scope: %s, ClaimKey: %s, ClaimValue: %s",
			config.Name, config.Issuer, config.ClientID, config.Scope, config.ClaimKey, config.ClaimValue)
	}

	mgr := &Manager{session: session}

	// Get current logs and check version
	currentLogs, err := mgr.GetOIDCProviderLogs()
	if err != nil {
		log.Errorf("Failed to get current OIDC provider logs: %v", err)
		return err
	}

	// If the received version is the same or lower than current, skip the update
	if version <= currentLogs.OIDCVersion {
		log.Infof("Received version %d is not newer than current version %d, skipping update", version, currentLogs.OIDCVersion)
		return nil
	}

	// Initialize new logs with the received version
	newLogs := &OIDCProviderLogs{
		OIDCVersion:                  version,
		OIDCAction:                   action,
		OIDCActionSucceeded:          true,
		OIDCError:                    "",
		PasswordLoginAction:          passwordAction,
		PasswordLoginActionSucceeded: true,
		PasswordLoginError:           "",
	}

	// Handle password login actions
	if passwordAction != "" {
		if err := service.handlePasswordLoginAction(mgr, passwordAction); err != nil {
			log.Errorf("Password login action failed: %v", err)
			newLogs.PasswordLoginActionSucceeded = false
			newLogs.PasswordLoginError = err.Error()
		}
	}

	// Handle OIDC provider actions
	if err := service.handleOIDCProviderAction(mgr, action, config, providerID, newLogs); err != nil {
		log.Errorf("OIDC provider action failed: %v", err)
		newLogs.OIDCActionSucceeded = false
		newLogs.OIDCError = err.Error()
	}

	// Save the logs
	if err := mgr.SaveOIDCProviderLogs(newLogs); err != nil {
		log.Errorf("Failed to save OIDC provider logs: %v", err)
		return err
	}

	return nil
}
